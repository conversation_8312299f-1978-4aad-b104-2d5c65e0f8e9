import CrateServices
import Factory
import SwiftData
import SwiftUI

struct RecentView: View {
  enum DisplayMode {
    case preview
    case fullScreen
  }

  @ObservedObject private var viewModel: RecentViewModel
  @Binding private var selectedTab: NavView.Tab
  private let displayMode: DisplayMode
  private let deepLinkHandler: DeepLinkHandler
  @Environment(\.dismiss) private var dismiss
  @State private var showingDeleteAllConfirmation = false
  @State private var showingAddToCollectionSheet = false
  @State private var selectedContent: ContentDTO?
  @StateObject private var userState = Container.shared.userState.resolve()

  init(
    selectedTab: Binding<NavView.Tab>,
    deepLinkHandler: DeepLinkHandler,
    fullScreen: Bool = false,
    viewModel: RecentViewModel? = nil
  ) {
    _selectedTab = selectedTab
    self.deepLinkHandler = deepLinkHandler
    displayMode = fullScreen ? .fullScreen : .preview

    // Use provided view model or create a shared one
    self.viewModel = viewModel ?? RecentViewModel.shared
  }

  var body: some View {
    Group {
      switch displayMode {
      case .preview:
        previewContent
      case .fullScreen:
        fullScreenContent
      }
    }
    .onAppear {
      // Only load content if it's empty to avoid duplicate API calls
      if viewModel.content.isEmpty {
        viewModel.loadContent()
      }
      // Ensure consistent sorting when view appears
      viewModel.refreshContentSorting()
    }
    .sheet(
      isPresented: $showingAddToCollectionSheet,
      onDismiss: {
        // Don't reset the content selection immediately on dismiss to avoid issues
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
          selectedContent = nil
        }
      },
      content: {
        // Capture the selected content in a local constant to prevent issues
        if let contentItem = selectedContent {
          AddContentToCollectionSheet(
            content: contentItem,
            onDismiss: { showingAddToCollectionSheet = false }
          )
        } else {
          Text("No content selected")
            .foregroundColor(.secondary)
            .padding()
        }
      }
    )
    .overlay {
      if viewModel.isDeletingContent {
        Color.black.opacity(0.3)
          .ignoresSafeArea()
          .overlay {
            VStack {
              ProgressView()
                .scaleEffect(1.5)
                .tint(.white)

              Text("Deleting...")
                .foregroundColor(.white)
                .padding(.top, 12)
                .font(.headline)
            }
            .padding(24)
            .background(RoundedRectangle(cornerRadius: 12).fill(Color.gray.opacity(0.7)))
          }
          .transition(.opacity)
      }
    }
    .animation(.easeInOut, value: viewModel.isDeletingContent)
  }

  // MARK: - Preview Mode Content

  @ViewBuilder
  private var previewContent: some View {
    VStack(alignment: .leading, spacing: 20) {
      // Recent Header
      HStack {
        Text("Recent")
          .font(.title2)
          .fontWeight(.bold)
        Spacer()
        NavigationLink(
          destination: RecentView(
            selectedTab: $selectedTab,
            deepLinkHandler: deepLinkHandler,
            fullScreen: true,
            viewModel: viewModel
          )
        ) {
          Text("View All")
            .foregroundColor(.blue)
            .fontWeight(.semibold)
        }
      }
      .padding(.horizontal)

      if viewModel.isLoading {
        ProgressView()
          .frame(maxWidth: .infinity)
          .padding()
      } else if viewModel.content.isEmpty {
        emptyStateView
      } else {
        ScrollView(.horizontal, showsIndicators: false) {
          HStack(spacing: 12) {
            ForEach(viewModel.content.prefix(6)) { contentItem in
              Button(
                action: {
                  contentSelected(contentItem)
                },
                label: {
                  NFCRecordRow(content: contentItem)
                })
            }
          }
          .padding(.horizontal)
        }
      }

      Divider()
        .padding(.vertical, 8)

      // Always embed CollectionView even when there are no tracks
      CollectionView(
        selectedTab: $selectedTab,
        deepLinkHandler: deepLinkHandler,
        collectionService: Container.shared.collectionService.resolve()
      )
    }
  }

  // MARK: - Empty State View

  private var emptyStateView: some View {
    VStack(spacing: 16) {
      Text("Write an NFC Record to add to this section.")
        .font(.footnote)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
        .padding(.top, 8)
        .padding(.bottom, 4)
      HStack {
        CrateButtonComponent(
          title: "Trending",
          action: { selectedTab = .trending }
        )
        .frame(maxWidth: .infinity)

        CrateButtonComponent(
          title: "Write",
          action: { selectedTab = .write }
        )
        .frame(maxWidth: .infinity)
      }
    }
    .padding(.horizontal)
  }

  // MARK: - Full Screen Mode Content

  private var fullScreenContent: some View {
    List {
      if viewModel.isLoading {
        ProgressView("Loading...")
          .frame(maxWidth: .infinity, alignment: .center)
          .padding()
          .listRowInsets(EdgeInsets())
          .listRowBackground(Color.clear)
      } else if viewModel.content.isEmpty {
        emptyStateView
          .frame(maxWidth: .infinity, alignment: .center)
          .padding()
          .listRowInsets(EdgeInsets())
          .listRowBackground(Color.clear)
      } else {
        ForEach(viewModel.content) { contentItem in
          HStack {
            Button(
              action: { contentSelected(contentItem) },
              label: {
                RecentWritesItemRow(content: contentItem)
                  .padding(.vertical, 8)
              })

            Spacer()

            // Add + button for adding to collection
            Button(
              action: {
                selectedContent = contentItem
                showingAddToCollectionSheet = true
              },
              label: {
                Image(systemName: "plus.circle.fill")
                  .foregroundColor(.blue)
                  .font(.title3)
                  .contentShape(Rectangle())
                  .frame(width: 44, height: 44)
              }
            )
            .buttonStyle(BorderlessButtonStyle())
            .padding(.trailing, 8)
          }
          .listRowInsets(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 0))
          .listRowBackground(Color.clear)
          .swipeActions(edge: .trailing) {
            Button(role: .destructive) {
              viewModel.deleteContent(contentItem)
            } label: {
              Label("Delete", systemImage: "trash")
            }
          }
        }
        .onDelete(perform: viewModel.removeContent)
      }
    }
    .listStyle(.plain)
    .listSectionSeparator(.hidden)
    .navigationTitle("Recent")
    .navigationBarTitleDisplayMode(.large)
    .toolbar {
      ToolbarItem(placement: .navigationBarTrailing) {
        Button(
          action: {
            showingDeleteAllConfirmation = true
          },
          label: {
            Image(systemName: "trash")
              .foregroundColor(.primary)
          }
        )
        .disabled(viewModel.content.isEmpty)
      }
    }
    .alert("Delete All Recent", isPresented: $showingDeleteAllConfirmation) {
      Button("Cancel", role: .cancel) {}
      Button("Delete All", role: .destructive) {
        viewModel.clearAllContent()
      }
    } message: {
      Text(
        "Are you sure you want to delete all recent activity? This will remove all content from both your device and the server."
      )
    }
  }

  // MARK: - Supporting Views

  struct NFCRecordRow: View {
    let content: ContentDTO

    var body: some View {
      VStack(alignment: .leading, spacing: 8) {
        if let imageUrl = content.mediaUrl.flatMap({ URL(string: $0) }) {
          CachedAsyncImage(url: imageUrl)
        } else {
          Image(systemName: "globe")
            .resizable()
            .scaledToFit()
            .frame(width: 160, height: 160)
            .foregroundColor(.gray)
        }

        VStack(alignment: .leading, spacing: 4) {
          Text(content.title ?? "Web URL")
            .font(.system(size: 16, weight: .semibold))
            .lineLimit(1)
          Text((content.detail ?? content.url) ?? "-")
            .lineLimit(1)
            .truncationMode(.tail)
            .font(.system(size: 14))
            .foregroundColor(.gray)
            .lineLimit(1)
        }
      }
      .frame(width: 160)
    }
  }

  struct RecentWritesItemRow: View {
    let content: ContentDTO

    var body: some View {
      HStack(alignment: .center, spacing: 12) {
        if let imageUrl = content.mediaUrl.flatMap({ URL(string: $0) }) {
          AsyncImage(url: imageUrl) { image in
            image.resizable()
              .aspectRatio(contentMode: .fill)
              .frame(width: 60, height: 60)
              .cornerRadius(8)
          } placeholder: {
            ProgressView()
              .frame(width: 60, height: 60)
          }
        } else {
          // If no valid URL, display the default placeholder.
          Image(systemName: "globe")
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 60, height: 60)
            .foregroundColor(.gray)
        }

        VStack(alignment: .leading, spacing: 4) {
          Text(content.title ?? "Web URL")
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.blue)
            .lineLimit(1)
          Text((content.detail ?? content.url) ?? "-")
            .font(.system(size: 14))
            .foregroundColor(.gray)
            .lineLimit(1)
            .truncationMode(.tail)
        }
        Spacer()
      }
      .frame(maxWidth: .infinity)
      .contentShape(Rectangle())
    }
  }

  // MARK: - Helper Methods

  private func contentSelected(_ contentItem: ContentDTO) {
    deepLinkHandler.deepLinkURL = URL(string: contentItem.url ?? "")
    selectedTab = .write
  }
}
