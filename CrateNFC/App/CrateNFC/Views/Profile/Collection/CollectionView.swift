import CrateServices
import Factory
import SwiftData
import SwiftUI

struct CollectionView: View {
  enum DisplayMode {
    case collectionsList  // For showing the list of collections
    case singleCollection  // For showing a single collection detail
  }

  @ObservedObject private var viewModel: CollectionViewModel
  @StateObject private var userState = Container.shared.userState.resolve()
  @State private var showingCreateCollectionSheet = false
  @State private var showingLoginSheet = false
  @State private var newCollectionName: String = ""
  @State private var showingClearConfirmation = false
  @State private var showingDeleteConfirmation = false
  @State private var selectedContent: ContentDTO?
  @State private var showingAddToCollectionSheet = false
  @Environment(\.dismiss) private var dismiss

  // Navigation and display properties
  private let displayMode: DisplayMode
  private let collection: CollectionDTO?
  @Binding private var selectedTab: NavView.Tab
  private let deepLinkHandler: DeepLinkHandler
  private let collectionService: CollectionServiceProtocol
  private let crateActor: CrateActor

  // Add state variable for showing add track sheet
  @State private var showingAddTrackSheet = false

  // Single initializer that handles both cases
  init(
    collection: CollectionDTO? = nil,
    selectedTab: Binding<NavView.Tab>,
    deepLinkHandler: DeepLinkHandler,
    collectionService: CollectionServiceProtocol,
    viewModel: CollectionViewModel? = nil
  ) {
    self.collection = collection
    displayMode = collection == nil ? .collectionsList : .singleCollection
    _selectedTab = selectedTab
    self.deepLinkHandler = deepLinkHandler
    self.collectionService = collectionService
    self.crateActor = Container.shared.crateActor.resolve()

    // Use provided view model or shared singleton
    self.viewModel = viewModel ?? CollectionViewModel.shared
  }

  var body: some View {
    Group {
      switch displayMode {
      case .collectionsList:
        collectionsListView
      case .singleCollection:
        if let currentCollection = collection {
          singleCollectionView
            .onAppear {
              if viewModel.currentCollection?.serverId != currentCollection.serverId {
                viewModel.setCurrentCollection(currentCollection)
              }
            }
        } else {
          Text("Collection not found")
            .foregroundColor(.secondary)
        }
      }
    }
    .onAppear {
      if displayMode == .collectionsList {
        Task {
          // Only load if collections are empty to avoid duplicate API calls
          if viewModel.collections.isEmpty {
            await viewModel.initialLoad()
          }
        }
      }
    }
    .sheet(
      isPresented: $showingAddToCollectionSheet,
      onDismiss: {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
          selectedContent = nil
        }
      },
      content: {
        if let content = selectedContent {
          AddContentToCollectionSheet(
            content: content,
            onDismiss: { showingAddToCollectionSheet = false }
          )
        } else {
          Text("No content selected")
            .foregroundColor(.secondary)
            .padding()
        }
      }
    )
    .overlay {
      if viewModel.isDeleting {
        Color.black.opacity(0.3)
          .ignoresSafeArea()
          .overlay {
            VStack {
              ProgressView()
                .scaleEffect(1.5)
                .tint(.white)

              Text("Deleting...")
                .foregroundColor(.white)
                .padding(.top, 12)
                .font(.headline)
            }
            .padding(24)
            .background(RoundedRectangle(cornerRadius: 12).fill(Color.gray.opacity(0.7)))
          }
          .transition(.opacity)
      }
    }
    .animation(.easeInOut, value: viewModel.isDeleting)
    .sheet(isPresented: $showingAddTrackSheet) {
      if let currentCollection = viewModel.currentCollection {
        AddUrlToCollectionSheet(
          collection: currentCollection.toModel(),
          onContentAdded: {
            // Refresh the current collection to show the newly added content
            Task {
              await viewModel.loadCollections()
              // Also refresh the current collection specifically
              if let collectionId = currentCollection.serverId {
                if let refreshedCollection = try? await crateActor.fetchCollectionDTO(
                    id: collectionId) {
                  await MainActor.run {
                    viewModel.setCurrentCollection(refreshedCollection)
                  }
                }
              }
            }
          }
        )
      }
    }
    .sheet(isPresented: $showingCreateCollectionSheet) {
      createCollectionView
    }
    .sheet(isPresented: $showingLoginSheet) {
      ProfileLoginView()
    }
  }

  // MARK: - Collections List View

  @ViewBuilder
  private var collectionsListView: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        Text("Collections")
          .font(.title2)
          .fontWeight(.bold)
        Spacer()

        if userState.isSignedIn {
          Button(
            action: {
              showingCreateCollectionSheet = true
            },
            label: {
              Image(systemName: "plus.circle.fill")
                .font(.title2)
                .foregroundColor(.blue)
            })
        }
      }
      .padding(.horizontal)

      if viewModel.isLoading {
        ProgressView()
          .frame(maxWidth: .infinity, alignment: .center)
          .padding(.top, 20)
      } else if !userState.isSignedIn {
        unauthenticatedView
      } else if viewModel.collections.isEmpty {
        Text("Add a Collection to Organize Content")
          .foregroundColor(.secondary)
          .font(.subheadline)
          .multilineTextAlignment(.center)
          .padding(.horizontal)
          .padding(.top, 8)
          .frame(maxWidth: .infinity)
      } else {
        ScrollView {
          VStack(spacing: 0) {
            ForEach(viewModel.collections, id: \.serverId) { collection in
              let destination = CollectionView(
                collection: collection,
                selectedTab: $selectedTab,
                deepLinkHandler: deepLinkHandler,
                collectionService: collectionService
              )
              NavigationLink(destination: destination) {
                collectionRow(collection)
              }
              .onTapGesture {}
              if collection.serverId != viewModel.collections.last?.serverId {
                Divider().padding(.leading)
              }
            }
          }
        }
      }
    }
    .sheet(
      isPresented: $showingCreateCollectionSheet,
      onDismiss: {},
      content: {
        createCollectionView
      }
    )
  }

  // MARK: - Single Collection View

  @ViewBuilder
  private var singleCollectionView: some View {
    VStack(alignment: .leading, spacing: 0) {
      collectionHeader

      trackCountIndicator

      Divider()
        .padding(.bottom, 8)

      if viewModel.currentCollection?.contents?.isEmpty ?? true {
        emptyContentView
      } else {
        contentsList
      }
    }
    .navigationBarBackButtonHidden(false)
    .alert("Clear Collection", isPresented: $showingClearConfirmation) {
      Button("Cancel", role: .cancel) {}
      Button("Clear All", role: .destructive) {
        if let currentCollection = viewModel.currentCollection {
          Task {
            try? await viewModel.clearAllContent(from: currentCollection)
          }
        }
      }
    } message: {
      Text("Are you sure you want to remove all content from this collection?")
    }
    .alert("Delete Collection", isPresented: $showingDeleteConfirmation) {
      Button("Cancel", role: .cancel) {}
      Button("Delete", role: .destructive) {
        if let currentCollection = viewModel.currentCollection {
          Task {
            do {
              try await viewModel.deleteCollection(currentCollection)
              dismiss()
            } catch {
              print("Error deleting collection: \(error)")
            }
          }
        }
      }
    } message: {
      Text("Are you sure you want to delete this collection? This action cannot be undone.")
    }
  }

  @ViewBuilder
  private var collectionHeader: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Text(viewModel.currentCollection?.name ?? "Unnamed Collection")
          .font(.title2)
          .fontWeight(.bold)

        Spacer()

        Menu {
          Button {
            showingAddTrackSheet = true
          } label: {
            Label("Add Content", systemImage: "plus")
          }

          Button {
            showingClearConfirmation = true
          } label: {
            Label("Clear All Content", systemImage: "clear")
          }

          Button(role: .destructive) {
            showingDeleteConfirmation = true
          } label: {
            Label("Delete Collection", systemImage: "trash")
          }
        } label: {
          Image(systemName: "ellipsis.circle")
            .foregroundColor(.blue)
        }
      }
    }
    .padding(.horizontal)
  }

  @ViewBuilder
  private var trackCountIndicator: some View {
    Text(
      "\(viewModel.currentCollection?.contents?.count ?? 0) item\(viewModel.currentCollection?.contents?.count == 1 ? "" : "s")"
    )
    .font(.subheadline)
    .foregroundColor(.gray)
    .padding(.horizontal)
    .padding(.bottom, 16)
  }

  @ViewBuilder
  private var emptyContentView: some View {
    VStack {
      Spacer()
      Text("No content in this collection")
        .foregroundColor(.secondary)
        .padding()
      Spacer()
    }
  }

  @ViewBuilder
  private var contentsList: some View {
    List {
      ForEach(
        viewModel.currentCollection?.contents?.sorted(by: {
          ($0.createdAt ?? .distantPast) > ($1.createdAt ?? .distantPast)
        }) ?? [], id: \.serverId
      ) { content in
        contentRowWithSwipe(content)
      }
    }
    .listStyle(PlainListStyle())
  }

  // Content row with swipe to delete
  private func contentRowWithSwipe(_ content: ContentDTO) -> some View {
    HStack {
      Button(
        action: { contentSelected(content) },
        label: {
          contentRow(content)
            .contentShape(Rectangle())
        }
      )
      .buttonStyle(PlainButtonStyle())

      // Add + button for adding to collection
      Button(
        action: {
          selectedContent = content
          showingAddToCollectionSheet = true
        },
        label: {
          Image(systemName: "plus.circle.fill")
            .foregroundColor(.blue)
            .font(.title3)
            .contentShape(Rectangle())
            .frame(width: 44, height: 44)
        }
      )
      .buttonStyle(BorderlessButtonStyle())
      .padding(.trailing, 8)
    }
    .swipeActions(edge: .trailing) {
      Button(role: .destructive) {
        if let currentCollection = viewModel.currentCollection {
          Task {
            await viewModel.deleteContentFromCollection(
              content, fromCollectionDTO: currentCollection)
          }
        }
      } label: {
        Label("Delete", systemImage: "trash")
      }
    }
    .swipeActions(edge: .leading) {
      Button {
        selectedContent = content
        showingAddToCollectionSheet = true
      } label: {
        Label("Add to Collection", systemImage: "folder.badge.plus")
      }
      .tint(.blue)
    }
  }

  private func contentRow(_ content: ContentDTO) -> some View {
    HStack(alignment: .center, spacing: 12) {
      // Content thumbnail or placeholder
      if let imageUrl = content.mediaUrl.flatMap({ URL(string: $0) }) {
        AsyncImage(url: imageUrl) { image in
          image.resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 60, height: 60)
            .cornerRadius(8)
        } placeholder: {
          ProgressView()
            .frame(width: 60, height: 60)
        }
      } else {
        // Default placeholder for content without images
        Image(systemName: "music.note")
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 60, height: 60)
          .padding(12)
          .background(Color.gray.opacity(0.2))
          .cornerRadius(8)
      }

      // Content title and detail
      VStack(alignment: .leading, spacing: 4) {
        Text(content.title ?? "Untitled Content")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.blue)
          .lineLimit(1)

        Text(content.detail ?? content.url ?? "-")
          .font(.system(size: 14))
          .foregroundColor(.gray)
          .lineLimit(1)
          .truncationMode(.tail)
      }

      Spacer()
    }
    .padding(.vertical, 8)
  }

  private func collectionRow(_ collection: CollectionDTO) -> some View {
    HStack {
      VStack(alignment: .leading, spacing: 4) {
        Text(collection.name ?? "Unnamed Collection")
          .font(.headline)
          .foregroundColor(.blue)

        Text("\(collection.contents?.count ?? 0) item\(collection.contents?.count == 1 ? "" : "s")")
          .font(.subheadline)
          .foregroundColor(.gray)
      }

      Spacer()

      // Chevron indicator for navigation
      Image(systemName: "chevron.right")
        .foregroundColor(.gray)
        .font(.system(size: 14))
    }
    .padding(.vertical, 12)
    .padding(.horizontal)
  }

  private var createCollectionView: some View {
    NavigationView {
      VStack(spacing: 20) {
        TextField("Collection Name", text: $newCollectionName)
          .padding()
          .background(Color(UIColor.secondarySystemBackground))
          .cornerRadius(8)
          .padding(.horizontal)

        Spacer()
      }
      .padding(.top, 20)
      .navigationTitle("New Collection")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarLeading) {
          Button("Cancel") {
            newCollectionName = ""
            showingCreateCollectionSheet = false
          }
        }

        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Create") {
            if !newCollectionName.isEmpty {
              Task {
                do {
                  try await viewModel.createCollection(name: newCollectionName)

                  if let content = selectedContent {
                    if let newCollection = viewModel.collections.last {
                      try await viewModel.addContentToCollection(
                        newCollection, contentToAddDTO: content)
                      selectedContent = nil
                    }
                  }

                  newCollectionName = ""
                  showingCreateCollectionSheet = false
                } catch {
                  print("Collection creation error: \(error)")
                }
              }
            }
          }
          .disabled(newCollectionName.isEmpty)
        }
      }
    }
  }

  // MARK: - Unauthenticated View

  @ViewBuilder
  private var unauthenticatedView: some View {
    VStack(spacing: 16) {
      Text("Sign in to create and manage your collections")
        .font(.subheadline)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      Button(
        action: {
          showingLoginSheet = true
        },
        label: {
          HStack {
            Image(systemName: "person.crop.circle")
            Text("Sign In")
          }
          .font(.headline)
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 12)
          .background(Color.blue)
          .cornerRadius(8)
        }
      )
    }
    .padding(.horizontal)
    .padding(.top, 40)
    .frame(maxWidth: .infinity)
  }

  // MARK: - Helper Methods

  private func contentSelected(_ content: ContentDTO) {
    deepLinkHandler.deepLinkURL = URL(string: content.url ?? "")
    selectedTab = .write
  }
}
