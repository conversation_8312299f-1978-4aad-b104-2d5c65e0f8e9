import Combine
import CrateServices
import Factory
import SwiftData
import SwiftUI

@MainActor
public final class CollectionViewModel: ObservableObject {
  @Published public private(set) var collections: [CollectionDTO] = []
  @Published public private(set) var currentCollection: CollectionDTO?
  @Published public private(set) var isLoading = false
  @Published public private(set) var isDeleting = false
  @Published var isRefreshingFromServer: Bool = false  // For pull-to-refresh or specific refresh button
  @Published var refreshError: String?  // For displaying errors to the user

  // Services
  private let collectionService: CollectionServiceProtocol
  private let contentService: ContentServiceProtocol
  private let userState: UserState
  private let crateActor: CrateActor

  // Cancellables for tracking subscriptions
  private var cancellables = Set<AnyCancellable>()

  // Add state tracking to prevent infinite loops
  private var lastServerFetchTime: Date?
  private let minimumRefreshInterval: TimeInterval = 5.0  // 5 seconds between refreshes

  // Shared singleton instance
  public static let shared = CollectionViewModel(
    collectionService: Container.shared.collectionService.resolve())

  private func convertCollectionDTOToModel(_ dto: CollectionDTO) -> Collection {
    Collection(
      serverId: dto.serverId,
      name: dto.name,
      content: dto.contents?.map { $0.toModel() } ?? [],
      thumbnail: dto.thumbnail,
      createdAt: dto.createdAt ?? Date.distantPast,
      updatedAt: dto.updatedAt ?? Date.distantPast
    )
  }

  public init(collectionService: CollectionServiceProtocol) {
    self.collectionService = collectionService
    self.crateActor = Container.shared.crateActor.resolve()
    contentService = Container.shared.contentService.resolve()
    userState = Container.shared.userState.resolve()

    // Subscribe to Auth events with receive(on:) to ensure main thread
    AuthPublisher.shared.publisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] event in
        guard let self = self else { return }

        switch event {
        case .signedIn:
          Task {
            await self.loadCollections()
          }
        case .signedOut:
          Task {
            await self.clearAllCollections()
          }
        case .tokenRefreshed, .profileUpdated, .userCreated:
          break
        }
      }
      .store(in: &cancellables)

    // Initial load based on auth state
    if userState.isSignedIn {
      Task {
        await self.loadCollections()
      }
    } else {
      Task {
        await self.loadLocalCollections()
      }
    }
  }

  // MARK: - Collection Management

  public func loadCollections() async {
    isLoading = true
    defer { isLoading = false }
    do {
      let localCollections = try await crateActor.fetchCollections()
      collections = localCollections
    } catch {
      collections = []
    }
  }

  public func loadLocalCollections() async {
    // Since the function is @MainActor, direct assignment is fine here
    isLoading = true
    refreshError = nil

    // 'defer' ensures this block of code is executed just before
    // the current scope (this function) is exited,
    // regardless of how it's exited (normal return or error thrown).
    // And because this function is @MainActor, the defer block will also execute on MainActor.
    defer {
      isLoading = false
    }

    do {
      let localCollections = try await crateActor.fetchCollections()
      // Back on Main Actor after await
      self.collections = localCollections
    } catch {
      print("Error loading local collections: \(error.localizedDescription)")
      // Back on Main Actor after await (if any was in a more complex catch)
      // or if no await, still on Main Actor.
      self.refreshError = "Failed to load local collections."
      self.collections = []
    }
    // 'isLoading = false' will be called here due to defer
  }

  public func refreshCollectionsFromServer() async {
    if isRefreshingFromServer { return }

    // All these direct assignments are fine because we're @MainActor
    isRefreshingFromServer = true
    isLoading = true
    refreshError = nil

    // 'defer' will run just before this function exits.
    // Since the function is @MainActor, and even after awaits we return to MainActor,
    // this defer block will execute on the MainActor.
    defer {
      isLoading = false
      isRefreshingFromServer = false  // Also reset this here if it's a general loading pattern
    }

    do {
      let serverCollections = try await collectionService.getCollections(start: 0, size: 50)
      try await crateActor.updateCollectionsFromDTOs(serverCollections)
      let localCollections = try await crateActor.fetchCollections()

      self.collections = localCollections
      // isLoading and isRefreshingFromServer will be set to false by defer
    } catch {
      print("Error refreshing collections from server: \(error.localizedDescription)")
      self.refreshError =
        "Failed to update collections. Showing local data. \n(\(error.localizedDescription))"
      // isLoading and isRefreshingFromServer will be set to false by defer

      // Call loadLocalCollections. Its own defer will handle its isLoading.
      // If this refresh fails, we still want isLoading/isRefreshingFromServer for *this* function
      // to be reset, which the defer above handles.
      await self.loadLocalCollections()
    }
  }
  // Initial load method (could be called in onAppear or init)
  public func initialLoad() async {
    // Since the ViewModel already loads collections on init based on auth state,
    // we only need to refresh from server if user is signed in and we haven't loaded yet
    if userState.isSignedIn && collections.isEmpty {
      await refreshCollectionsFromServer()
    } else if !userState.isSignedIn {
      await loadLocalCollections()
    }
    // If collections are already loaded, do nothing to avoid duplicate API calls
  }

  private func updateLocalCollections(_ serverCollections: [ServerCollectionDTO]) async throws {
    try await crateActor.syncCollectionsFromServer(serverCollections)
  }

  public func createCollection(name: String) async throws {
    isLoading = true
    defer { isLoading = false }
    let serverCollection = try await collectionService.createEmptyCollection(
      name: name, thumbnail: nil)
    try await crateActor.addCollection(
      serverId: serverCollection.id,
      name: serverCollection.name,
      thumbnail: serverCollection.thumbnail,
      content: []
    )
    let localCollections = try await crateActor.fetchCollections()
    collections = localCollections
  }

  public func deleteCollection(_ collection: CollectionDTO) async throws {
    isDeleting = true
    defer { isDeleting = false }
    if let serverId = collection.serverId {
      _ = try await collectionService.deleteCollection(serverId: serverId)
      try await crateActor.deleteCollection(serverId: serverId)
    }
    let localCollections = try await crateActor.fetchCollections()
    collections = localCollections
  }

  public func setCurrentCollection(_ collection: CollectionDTO) {
    currentCollection = collection
  }

  public func addContentToCollection(
    _ targetCollectionDTO: CollectionDTO, contentToAddDTO: ContentDTO
  ) async throws {

    guard let collectionServerId = targetCollectionDTO.serverId,
          let contentServerId = contentToAddDTO.serverId
    else {
      print("Error: Collection or Content DTO is missing a serverId.")
      // Consider throwing a ViewModel-specific error here instead of just returning
      // throw ViewModelError.missingIdentifier
      return
    }

    do {
      // First update the server
      let success = try await collectionService.addContentToCollection(
        collectionServerId: collectionServerId,
        contentServerId: contentServerId
      )

      if success {
        print("Successfully added content to collection via service.")

        // Update local database after server success
        try await crateActor.addContent(
          contentToAddDTO, toCollectionWithServerId: collectionServerId)

        // Refresh the UI by updating the collections array
        if let refreshedCollectionDTO = try? await crateActor.fetchCollectionDTO(
            id: collectionServerId) {
          if let idx = self.collections.firstIndex(where: { $0.serverId == collectionServerId }) {
            self.collections[idx] = refreshedCollectionDTO
          }
          if self.currentCollection?.serverId == collectionServerId {
            self.currentCollection = refreshedCollectionDTO
          }
        }

        print("UI state updated after successful content addition.")
      } else {
        print("Service reported failure to add content to collection.")
        self.refreshError = "Failed to add content to collection on the server."
        throw CollectionServiceError.addContentToCollectionFailed
      }
    } catch {
      print("Error calling addContentToCollection service: \(error.localizedDescription)")
      self.refreshError = "An error occurred: \(error.localizedDescription)"
      throw error
    }

  }

  public func deleteContentFromCollection(
    _ contentDTOToDelete: ContentDTO, fromCollectionDTO: CollectionDTO
  ) async {
    guard let collectionServerId = fromCollectionDTO.serverId,
          let contentServerIdToDelete = contentDTOToDelete.serverId
    else {
      self.refreshError = "Cannot delete content: Missing necessary identifiers."
      return  // Or throw an error if the function is marked as throws and that's preferred
    }

    self.isLoading = true
    self.refreshError = nil  // Clear previous errors
    defer {
      self.isLoading = false  // Ensure isLoading is reset
    }

    do {
      // --- Step 1: Attempt to remove from the server API ---
      // Assuming collectionService.removeContentFromCollection throws on error OR returns a Bool
      let apiSuccess = try await collectionService.removeContentFromCollection(
        collectionServerId: collectionServerId,
        contentServerId: contentServerIdToDelete
      )

      if !apiSuccess {
        // API reported failure explicitly (if it returns Bool and didn't throw)
        print("Service reported failure to remove content from collection on the server.")
        self.refreshError = "Could not remove content from server. Please try again."
        return  // Exit if API call logically failed but didn't throw
      }
      print("API: Content removed successfully.")

      // --- Step 2: Local SwiftData Update via CrateActor ---
      try await crateActor.removeContent(
        contentServerId: contentServerIdToDelete,
        fromCollectionWithServerId: collectionServerId
      )
      print("Local DB: Content removed successfully.")

      // --- Step 3: ALL Operations Succeeded - NOW Update Local ViewModel DTOs for UI ---

      // Option B: Re-fetch the updated CollectionDTO from CrateActor (more robust for consistency)
      // This ensures you have the absolute latest state, including any server-side changes
      // or precise 'updatedAt' timestamps from the CrateActor.

      if let refreshedCollectionDTO = try? await crateActor.fetchCollectionDTO(
          id: collectionServerId) {
        if let idx = self.collections.firstIndex(where: { $0.serverId == collectionServerId }) {
          self.collections[idx] = refreshedCollectionDTO
        }
        if self.currentCollection?.serverId == collectionServerId {
          self.currentCollection = refreshedCollectionDTO
        }
      } else {
        // If collection not found after content removal (unlikely if just content removed)
        self.collections.removeAll(where: { $0.serverId == collectionServerId })
        if self.currentCollection?.serverId == collectionServerId {
          self.currentCollection = nil
        }
      }

      print("UI state updated after successful deletion.")

    } catch {
      print("Error during content deletion: \(error.localizedDescription)")
      self.refreshError = "Failed to delete content. \(error.localizedDescription)"
      // No optimistic UI update was made, so no explicit rollback needed.
      // The UI still shows the state before the attempted deletion until a successful operation or full refresh.
      // Optionally, re-throw if the View needs to handle this error for specific UI alerts.
      // If the function is not marked 'throws', this 'catch' handles the error by setting refreshError.
    }
  }

  public func clearAllContent(from collection: CollectionDTO) async throws {
    guard let contents = collection.contents else { return }
    for content in contents {
      await deleteContentFromCollection(content, fromCollectionDTO: collection)
    }
  }

  private func clearAllCollections() async {
    do {
      try await crateActor.clearAllCollections()

      // Update the published collections array
      await MainActor.run {
        self.collections = []
        self.currentCollection = nil
      }
    } catch {
      print("Error clearing collections: \(error.localizedDescription)")
    }
  }

  public func unfurlAndAddContent(url: String) async throws {
    guard let collection = currentCollection else {
      throw CollectionServiceError.notAuthenticated
    }

    // First unfurl the URL and get the content directly
    let contentData = try await contentService.unfurl(url: url)
    // Add the content to the current collection
    try await addContentToCollection(collection, contentToAddDTO: contentData)
  }
}
