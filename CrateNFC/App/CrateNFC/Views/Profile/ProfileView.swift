import CrateServices
import Factory
import SwiftData
import SwiftUI

struct ProfileView: View {
  @StateObject private var viewModel: ProfileViewModel
  @StateObject private var userState = Container.shared.userState.resolve()
  @Binding private var selectedTab: NavView.Tab

  public init(selectedTab: Binding<NavView.Tab>, deepLinkHandler: DeepLinkHandler) {
    _selectedTab = selectedTab
    _viewModel = StateObject(wrappedValue: ProfileViewModel(deepLinkHandler: deepLinkHandler))
  }

  var body: some View {
    NavigationView {
      VStack {
        ProfileViewOptions(
          showingSettings: $viewModel.showingSettings,
          showingLogin: $viewModel.showingLogin,
          isAuthenticated: userState.isSignedIn
        )

        getMainContent()
        Spacer()
      }
      .sheet(isPresented: $viewModel.showingSettings) {
        AppSettingsView()
      }
      .sheet(isPresented: $viewModel.showingLogin) {
        ProfileLoginView()
      }
      .onAppear {
        viewModel.getNfcRecords()
      }
      .padding()
      .background(Color(UIColor.systemBackground))
      .refreshable {
        viewModel.getNfcRecords()
      }
    }
  }

  @ViewBuilder
  private func getMainContent() -> some View {
    switch viewModel.state {
    case .loading:
      getLoadingState()
    case let .error(message):
      getErrorState(message)
    case .empty, .content:
      ScrollView(.vertical, showsIndicators: false) {
        VStack(spacing: 24) {
          RecentView(
            selectedTab: $selectedTab,
            deepLinkHandler: viewModel.deepLinkHandler,
            viewModel: RecentViewModel.shared
          )
        }
        .padding(.bottom)
      }
    }
  }

  @ViewBuilder
  private func getErrorState(_ errorMessage: String) -> some View {
    Text(errorMessage)
      .foregroundColor(.red)
      .padding()
  }

  @ViewBuilder
  private func getLoadingState() -> some View {
    ProgressView("Loading...")
      .progressViewStyle(CircularProgressViewStyle())
      .padding()
  }
}

#Preview {
  ProfileView(
    selectedTab: .constant(.profile),
    deepLinkHandler: DeepLinkHandler()
  )
}
