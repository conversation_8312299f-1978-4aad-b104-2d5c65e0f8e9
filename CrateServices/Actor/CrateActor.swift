import Foundation
import SwiftData

/// Errors that can occur during Crate operations
public enum CrateError: Error {
  case collectionNotFound
  case contentNotFound
  case invalidData
  case saveFailed
}

/// Actor responsible for managing Crate data operations.
/// Handles CRUD operations for content, collections, and users, as well as managing relationships between them.
@available(iOS 17, *)
@ModelActor
public actor CrateActor {
  // No service dependencies; only thread-safe SwiftData access

  public func fetchCollectionDTO(id: Int) async throws -> CollectionDTO? {
    let predicate = #Predicate<Collection> { $0.serverId == id }
    guard let model = try modelContext.fetch(FetchDescriptor(predicate: predicate)).first else {
      return nil
    }
    return model.toDTO()  // Collection.toDTO() includes content DTOs
  }

  public func fetchCollections() async throws -> [CollectionDTO] {
    let fetchDescriptor = FetchDescriptor<Collection>(sortBy: [SortDescriptor(\Collection.name)])
    let collections = try self.modelContext.fetch(fetchDescriptor)
    return collections.map { collection in
      CollectionDTO(
        serverId: collection.serverId,
        name: collection.name,
        thumbnail: collection.thumbnail,
        contents: collection.content.map { content in
          ContentDTO(
            serverId: content.serverId,
            detail: content.detail,
            title: content.title,
            mediaUrl: content.mediaUrl,
            url: content.url,
            updatedAt: content.updatedAt,
            createdAt: content.createdAt
          )
        },
        createdAt: collection.createdAt,
        updatedAt: collection.updatedAt
      )
    }
  }

  // Example: Add a collection
  public func addCollection(
    serverId: Int?,
    name: String?,
    thumbnail: String?,
    content: [Content] = []
  ) async throws {
    let collection = Collection(
      serverId: serverId,
      name: name,
      content: content,
      thumbnail: thumbnail,
      createdAt: Date(),
      updatedAt: Date()
    )
    self.modelContext.insert(collection)
    try self.modelContext.save()
  }

  // Example: Delete a collection by serverId
  public func deleteCollection(serverId: Int) async throws {
    let predicate = #Predicate<Collection> { collection in
      collection.serverId == serverId
    }
    let fetchDescriptor = FetchDescriptor<Collection>(predicate: predicate)
    let collections = try self.modelContext.fetch(fetchDescriptor)
    for collection in collections {
      self.modelContext.delete(collection)
    }
    try self.modelContext.save()
  }

  // Synchronize local collections with an array of CollectionDTOs
  public func updateCollectionsFromDTOs(_ dtos: [CollectionDTO]) async throws {

    // TEMPORARILY CALL THE INSPECTION METHOD HERE FOR DEBUGGING:
    try await inspectDuplicateContentServerIds()
    // REMOVE OR COMMENT OUT THE ABOVE LINE AFTER DEBUGGING
    let allContent = try self.modelContext.fetch(FetchDescriptor<Content>())
    var contentByServerId = [Int: Content](
      uniqueKeysWithValues: allContent.compactMap { content in
        guard let id = content.serverId else { return nil }
        return (id, content)
      }
    )

    let existingCollections = try self.modelContext.fetch(FetchDescriptor<Collection>())
    var collectionByServerId = [Int: Collection](
      uniqueKeysWithValues: existingCollections.compactMap { collection in
        guard let id = collection.serverId else { return nil }
        return (id, collection)
      }
    )

    let dtoServerIds = Set(dtos.compactMap { $0.serverId })
    removeCollectionsNotInDTOs(existingCollections, dtoServerIds: dtoServerIds)
    updateOrCreateCollections(
      dtos, collectionByServerId: &collectionByServerId, contentByServerId: &contentByServerId)
    try self.modelContext.save()
  }

  private func removeCollectionsNotInDTOs(
    _ existingCollections: [Collection], dtoServerIds: Set<Int>
  ) {
    for collection in existingCollections {
      if let id = collection.serverId, !dtoServerIds.contains(id) {
        self.modelContext.delete(collection)
      }
    }
  }

  private func updateOrCreateCollections(
    _ dtos: [CollectionDTO], collectionByServerId: inout [Int: Collection],
    contentByServerId: inout [Int: Content]
  ) {
    for dto in dtos {
      guard let serverId = dto.serverId else { continue }
      if let existing = collectionByServerId[serverId] {
        updateCollection(existing, with: dto, contentByServerId: &contentByServerId)
      } else {
        let newCollection = Collection(
          serverId: serverId,
          name: dto.name,
          content: [],
          thumbnail: dto.thumbnail,
          createdAt: dto.createdAt,
          updatedAt: dto.updatedAt
        )
        if let dtoContents = dto.contents {
          for dtoContent in dtoContents {
            if let contentId = dtoContent.serverId,
               let content = contentByServerId[contentId] {
              newCollection.content.append(content)
            }
          }
        }
        self.modelContext.insert(newCollection)
        collectionByServerId[serverId] = newCollection
      }
    }
  }

  private func updateCollection(
    _ collection: Collection, with dto: CollectionDTO, contentByServerId: inout [Int: Content]
  ) {
    collection.name = dto.name
    collection.thumbnail = dto.thumbnail
    collection.updatedAt = dto.updatedAt
    collection.createdAt = dto.createdAt
    if let dtoContents = dto.contents {
      let dtoContentIds = dtoContents.compactMap { $0.serverId }
      collection.content.removeAll { content in
        guard let contentId = content.serverId else { return true }
        return !dtoContentIds.contains(contentId)
      }
      for dtoContent in dtoContents {
        if let contentId = dtoContent.serverId,
           let content = contentByServerId[contentId],
           !collection.content.contains(where: { $0 === content }) {
          collection.content.append(content)
        }
      }
    }
  }

  // MARK: Content in Collection Management

  // Function to check for content with duplicate serverIds
  public func inspectDuplicateContentServerIds() async throws {
    print("🔍 Starting inspection for duplicate Content serverIds...")
    let context = self.modelContext  // Use the actor's model context

    let allContentFetchDescriptor = FetchDescriptor<Content>(sortBy: [
      SortDescriptor(\Content.serverId)
    ])
    let allContent: [Content]
    do {
      allContent = try context.fetch(allContentFetchDescriptor)
    } catch {
      print("❌ Error fetching content: \(error.localizedDescription)")
      throw error
    }

    if allContent.isEmpty {
      print("ℹ️ No content found in the database.")
      return
    }

    print("ℹ️ Total content fetched: \(allContent.count)")

    // Group content by serverId
    // We can't use Dictionary(grouping:by:) directly if serverId is optional
    // and we want to handle nil serverIds separately or ignore them for duplication check.

    var contentGroupedByServerId = [Int: [Content]]()
    var contentWithNilServerIdCount = 0

    for content in allContent {
      if let serverId = content.serverId {
        contentGroupedByServerId[serverId, default: []].append(content)
      } else {
        contentWithNilServerIdCount += 1
      }
    }

    if contentWithNilServerIdCount > 0 {
      print("ℹ️ Found \(contentWithNilServerIdCount) content item(s) with a nil serverId.")
    }

    var foundDuplicates = false
    // Iterate ONLY over the groups that actually have duplicates
    for (serverId, contentInGroup) in contentGroupedByServerId where contentInGroup.count > 1 {
      foundDuplicates = true  // Now, if we enter this loop, we definitely have duplicates
      print("\n🚨 DUPLICATE serverId FOUND: \(serverId) (Count: \(contentInGroup.count))")
      for (index, content) in contentInGroup.enumerated() {
        print("  --- Duplicate Instance #\(index + 1) ---")
        print("    Content Title: \(content.title ?? "N/A")")
        print("    Detail: \(content.detail ?? "N/A")")
        print("    URL: \(content.url ?? "N/A")")
        print("    Media URL: \(content.mediaUrl ?? "N/A")")
        print("    Created At: \(content.createdAt?.description ?? "N/A")")
        print("    Updated At: \(content.updatedAt?.description ?? "N/A")")
        // print("    Persistent ID: \(content.persistentModelID.storeIdentifier ?? "N/A")")
      }
    }

    if !foundDuplicates {
      print("✅ No duplicate Content serverIds found (excluding content with nil serverId).")
    }
    print("🔍 Inspection finished.")
  }

  // Helper function to fetch or create a Content model
  private func fetchOrCreateContent(from dto: ContentDTO) throws -> Content {
    if let serverId = dto.serverId {
      // Try to fetch existing content by serverId
      let predicate = #Predicate<Content> { $0.serverId == serverId }
      let descriptor = FetchDescriptor<Content>(predicate: predicate)
      if let existingContent = try modelContext.fetch(descriptor).first {
        // Optionally update existing content's properties from DTO if needed
        // existingContent.title = dto.title ?? existingContent.title
        // ... etc. ...
        // existingContent.updatedAt = dto.updatedAt ?? existingContent.updatedAt ?? Date()
        return existingContent
      }
    }
    // If not found by serverId, or if serverId is nil, create a new one
    let newContent = dto.toModel()  // Uses ContentDTO.toModel()
    // If dto.serverId was nil, newContent.serverId will be nil.
    // If dto.serverId was provided but content not found, newContent.serverId will have that value.
    modelContext.insert(newContent)
    return newContent
  }

  public func addContent(_ contentDTO: ContentDTO, toCollectionWithServerId collectionServerId: Int)
  async throws {
    // 1. Fetch the Collection model
    let collectionPredicate = #Predicate<Collection> { $0.serverId == collectionServerId }
    guard
      let collectionModel = try modelContext.fetch(FetchDescriptor(predicate: collectionPredicate))
        .first
    else {
      throw CrateError.collectionNotFound
    }

    // 2. Fetch or Create the Content model
    let contentModel: Content

    if let contentServerId = contentDTO.serverId {
      // Check if we already have content with this serverId
      let contentPredicate = #Predicate<Content> { $0.serverId == contentServerId }
      if let existingContent = try modelContext.fetch(FetchDescriptor(predicate: contentPredicate))
          .first {
        contentModel = existingContent
        // Optionally update existing content's details from contentDTO
        contentModel.title = contentDTO.title ?? contentModel.title
        contentModel.detail = contentDTO.detail ?? contentModel.detail
        contentModel.mediaUrl = contentDTO.mediaUrl ?? contentModel.mediaUrl
        contentModel.url = contentDTO.url ?? contentModel.url
        contentModel.updatedAt = contentDTO.updatedAt ?? contentModel.updatedAt
      } else {
        // Create new content
        contentModel = Content(
          serverId: contentServerId,
          detail: contentDTO.detail,
          title: contentDTO.title,
          mediaUrl: contentDTO.mediaUrl,
          url: contentDTO.url,
          updatedAt: contentDTO.updatedAt ?? Date(),
          createdAt: contentDTO.createdAt ?? Date()
        )
        modelContext.insert(contentModel)
      }
    } else {
      // ContentDTO has no serverId, create local content
      contentModel = Content(
        serverId: nil,
        detail: contentDTO.detail,
        title: contentDTO.title,
        mediaUrl: contentDTO.mediaUrl,
        url: contentDTO.url,
        updatedAt: contentDTO.updatedAt ?? Date(),
        createdAt: contentDTO.createdAt ?? Date()
      )
      modelContext.insert(contentModel)
    }

    // 3. Add content to collection's content (if not already there)
    if !collectionModel.content.contains(where: {
      $0.persistentModelID == contentModel.persistentModelID
    }) {
      collectionModel.content.append(contentModel)
      collectionModel.updatedAt = Date()
    }

    // 4. Save changes
    try modelContext.save()
  }

  public func removeContent(
    contentServerId: Int, fromCollectionWithServerId collectionServerId: Int
  )
  async throws {
    // 1. Fetch the Collection model
    let collectionPredicate = #Predicate<Collection> { $0.serverId == collectionServerId }
    guard
      let collectionModel = try modelContext.fetch(FetchDescriptor(predicate: collectionPredicate))
        .first
    else {
      throw CrateError.collectionNotFound
    }

    // 2. Remove the content from the collection's content relationship
    //    It's important to remove based on a stable identifier.
    //    If serverId is reliable and unique for content within the DB:
    let initialContentCount = collectionModel.content.count
    collectionModel.content.removeAll { $0.serverId == contentServerId }

    if collectionModel.content.count < initialContentCount {
      // collectionModel.updatedAt = Date()  // Update timestamp if content was actually removed
      print(
        "Content with serverId \(contentServerId) removed from collection \(collectionServerId).")
    } else {
      print(
        "Content with serverId \(contentServerId) not found in collection \(collectionServerId). No changes made."
      )
      // Optionally, you could throw an error here if the content was expected to be present.
      // throw CrateError.contentNotFoundInCollection
    }

    // 3. Save changes
    //    Note: Deleting content from a collection's 'content' array only breaks the relationship.
    //    It does NOT delete the Content object itself from the database.
    //    If you wanted to delete the Content object if it's no longer in any collections,
    //    that would be more complex "orphan" cleanup logic.
    try modelContext.save()
  }

  // MARK: - Recent Collection Management

  public func fetchRecentCollection() async throws -> RecentCollectionDTO? {
    // Fetch the Recent collection with its content relationship loaded and sorted
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { collection in
        collection.name == "Recent"
      }
    )
    let collections = try self.modelContext.fetch(descriptor)

    guard let recentCollection = collections.first else {
      return nil
    }

    // For better performance, we could fetch content separately with sorting,
    // but SwiftData relationship predicates are complex. For now, sort in memory.
    // TODO: Investigate using @Relationship(deleteRule: .cascade, inverse: \Content.collections)
    // with proper fetch descriptors for database-level sorting
    let sortedContent = recentCollection.content.sorted { content1, content2 in
      let date1 = content1.createdAt ?? .distantPast
      let date2 = content2.createdAt ?? .distantPast
      return date1 > date2  // Newest first (by creation time)
    }

    return RecentCollectionDTO(
      name: recentCollection.name,
      thumbnail: recentCollection.thumbnail,
      contents: sortedContent.map { content in
        ContentDTO(
          serverId: content.serverId,
          detail: content.detail,
          title: content.title,
          mediaUrl: content.mediaUrl,
          url: content.url,
          updatedAt: content.updatedAt,
          createdAt: content.createdAt
        )
      },
      createdAt: recentCollection.createdAt,
      updatedAt: recentCollection.updatedAt
    )
  }

  public func addRecentCollection(name: String, thumbnail: String, content dtos: [ContentDTO])
  async throws {
    var contentModels: [Content] = []
    for dto in dtos {
      contentModels.append(try fetchOrCreateContent(from: dto))
    }

    let recentCollection = RecentCollection(
      name: name,
      content: contentModels,
      thumbnail: thumbnail,
      createdAt: Date(),
      updatedAt: Date()
    )
    self.modelContext.insert(recentCollection)
    try self.modelContext.save()
  }

  public func updateRecentCollection(content dtos: [ContentDTO]) async throws {
    print("🏪 CrateActor: updateRecentCollection called with \(dtos.count) DTOs")

    // Log the first few DTOs being passed in
    print("📋 CrateActor: DTOs being processed:")
    for (index, dto) in dtos.prefix(5).enumerated() {
      print(
        "  [\(index)] \(dto.title ?? "No title") - updatedAt: \(dto.updatedAt?.description ?? "nil")"
      )
    }

    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { $0.name == "Recent" }
    )
    guard let recentCollection = try modelContext.fetch(descriptor).first else {
      print("❌ CrateActor: Recent collection not found!")
      throw CrateError.collectionNotFound
    }

    var contentModels: [Content] = []
    for dto in dtos {
      let contentModel = try fetchOrCreateContent(from: dto)
      contentModels.append(contentModel)
    }

    print("📦 CrateActor: Created/fetched \(contentModels.count) content models")
    print("📋 CrateActor: Content models after conversion:")
    for (index, model) in contentModels.prefix(5).enumerated() {
      print(
        "  [\(index)] \(model.title ?? "No title") - updatedAt: \(model.updatedAt?.description ?? "nil")"
      )
    }

    recentCollection.content.removeAll()  // Clear existing relationships
    recentCollection.content.append(contentsOf: contentModels)  // Add resolved (existing or new) content
    recentCollection.updatedAt = Date()

    print("✅ CrateActor: Recent collection updated with \(recentCollection.content.count) items")

    try self.modelContext.save()
  }

  public func removeContentFromRecent(_ content: ContentDTO) async throws {
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { collection in
        collection.name == "Recent"
      }
    )
    let collections = try self.modelContext.fetch(descriptor)

    guard let recentCollection = collections.first else {
      throw CrateError.collectionNotFound
    }

    // Remove the content from the collection
    recentCollection.content.removeAll { $0.serverId == content.serverId }
    recentCollection.updatedAt = Date()

    try self.modelContext.save()
  }

  public func clearRecentCollection() async throws {
    let descriptor = FetchDescriptor<RecentCollection>(
      predicate: #Predicate<RecentCollection> { collection in
        collection.name == "Recent"
      }
    )
    let collections = try self.modelContext.fetch(descriptor)

    // Clear all content from each collection
    for collection in collections {
      collection.content.removeAll()
      collection.updatedAt = Date()
    }

    try self.modelContext.save()
  }

  // MARK: - NFC Record Operations (migrated from NFCRecordService)

  /// Saves a Content record to SwiftData (replaces NFCRecordService.save)
  public func saveContent(_ content: Content) async throws {
    modelContext.insert(content)
    try modelContext.save()
  }

  /// Gets all Content records with deduplication (replaces NFCRecordService.getAll)
  public func getAllContent() async throws -> [ContentDTO] {
    var fetchDescriptor = FetchDescriptor<Content>()
    fetchDescriptor.sortBy = [SortDescriptor(\Content.createdAt, order: .reverse)]
    fetchDescriptor.fetchLimit = 20

    let existingRecords = try modelContext.fetch(fetchDescriptor)

    let uniqueRecords = existingRecords.uniqued(on: { record in
      record.url ?? ""
    })
    return Array(uniqueRecords).map { $0.toDTO() }
  }

  /// Deletes all Content records (replaces NFCRecordService.deleteAll)
  public func deleteAllContent() async throws {
    try modelContext.delete(model: Content.self)
    try modelContext.save()
  }

  // MARK: - Trending Content Operations

  /// Gets all trending content from SwiftData and returns as DTOs
  public func getAllTrendingContent() async throws -> [ContentDTO] {
    var fetchDescriptor = FetchDescriptor<TrendingContent>()
    fetchDescriptor.sortBy = [SortDescriptor(\TrendingContent.createdAt, order: .reverse)]
    fetchDescriptor.fetchLimit = 20
    let existingTrendingContent = try modelContext.fetch(fetchDescriptor)

    let contentDTOs = existingTrendingContent.map { $0.toDTO() }
    let uniqueContentDTOs = contentDTOs.uniqued(on: { dto in
      "\(dto.title ?? ""):\(dto.url ?? "")"
    })
    return Array(uniqueContentDTOs)
  }

  /// Saves trending content DTOs to SwiftData as TrendingContent models
  public func saveTrendingContent(_ contentDTOs: [ContentDTO]) async throws {
    // Convert DTOs to TrendingContent models
    let trendingContent = contentDTOs.map { dto in
      TrendingContent(
        serverId: dto.serverId,
        detail: dto.detail,
        title: dto.title,
        mediaUrl: dto.mediaUrl,
        url: dto.url,
        updatedAt: dto.updatedAt ?? Date(),
        createdAt: dto.createdAt ?? Date()
      )
    }

    // Check for existing content with the same serverId and update them instead of inserting duplicates
    for item in trendingContent {
      if let serverId = item.serverId,
         let existingContent = try? getTrendingContentByServerId(serverId) {
        // Update existing content properties
        existingContent.detail = item.detail
        existingContent.title = item.title
        existingContent.mediaUrl = item.mediaUrl
        existingContent.url = item.url
        existingContent.updatedAt = item.updatedAt
      } else {
        // Insert new content
        modelContext.insert(item)
      }
    }

    try modelContext.save()
  }

  /// Deletes all trending content from SwiftData
  public func deleteAllTrendingContent() async throws {
    try modelContext.delete(model: TrendingContent.self)
    try modelContext.save()
  }

  /// Helper method to get trending content by serverId
  private func getTrendingContentByServerId(_ serverId: Int) throws -> TrendingContent? {
    let predicate = #Predicate<TrendingContent> { content in
      content.serverId == serverId
    }

    let descriptor = FetchDescriptor<TrendingContent>(predicate: predicate)
    let content = try modelContext.fetch(descriptor)
    return content.first
  }

  /// Gets content by serverId from SwiftData
  public func getContentByServerId(_ serverId: Int) async throws -> ContentDTO? {
    let predicate = #Predicate<Content> { content in
      content.serverId == serverId
    }

    let descriptor = FetchDescriptor<Content>(predicate: predicate)
    let content = try modelContext.fetch(descriptor)
    return content.first?.toDTO()
  }

  // MARK: - Collection Synchronization Operations

  /// Synchronizes local collections with server collections
  /// Converts ServerCollectionDTO to local models and updates SwiftData
  public func syncCollectionsFromServer(_ serverCollections: [ServerCollectionDTO]) async throws {
    let isoFormatter = ISO8601DateFormatter()

    let allContent = try modelContext.fetch(FetchDescriptor<Content>())
    var contentByServerId = [Int: Content](
      uniqueKeysWithValues: allContent.compactMap { content in
        guard let id = content.serverId else { return nil }
        return (id, content)
      }
    )

    let existingCollections = try modelContext.fetch(FetchDescriptor<Collection>())
    var collectionByServerId = [Int: Collection](
      uniqueKeysWithValues: existingCollections.compactMap { collection in
        guard let id = collection.serverId else { return nil }
        return (id, collection)
      }
    )

    // Remove collections that are no longer on the server
    let serverCollectionIds = Set(serverCollections.map { $0.id })
    for collection in existingCollections {
      if let id = collection.serverId, !serverCollectionIds.contains(id) {
        modelContext.delete(collection)
      }
    }

    // Process each server collection
    for serverCollection in serverCollections {
      try processServerCollection(
        serverCollection,
        collectionByServerId: &collectionByServerId,
        contentByServerId: &contentByServerId,
        isoFormatter: isoFormatter
      )
    }

    try modelContext.save()
  }

  /// Processes a single server collection and updates local data
  private func processServerCollection(
    _ serverCollection: ServerCollectionDTO,
    collectionByServerId: inout [Int: Collection],
    contentByServerId: inout [Int: Content],
    isoFormatter: ISO8601DateFormatter
  ) throws {
    if let existingCollection = collectionByServerId[serverCollection.id] {
      try updateExistingCollection(
        existingCollection,
        with: serverCollection,
        contentByServerId: &contentByServerId,
        isoFormatter: isoFormatter
      )
    } else {
      try createNewCollection(
        from: serverCollection,
        collectionByServerId: &collectionByServerId,
        contentByServerId: &contentByServerId,
        isoFormatter: isoFormatter
      )
    }
  }

  /// Updates an existing collection with server data
  private func updateExistingCollection(
    _ existingCollection: Collection,
    with serverCollection: ServerCollectionDTO,
    contentByServerId: inout [Int: Content],
    isoFormatter: ISO8601DateFormatter
  ) throws {
    existingCollection.name = serverCollection.name
    existingCollection.updatedAt = isoFormatter.date(from: serverCollection.updated)

    let serverContentIds = serverCollection.contents.compactMap { $0.id }
    existingCollection.content.removeAll { content in
      guard let contentId = content.serverId else { return true }
      return !serverContentIds.contains(contentId)
    }

    for serverContent in serverCollection.contents {
      guard let contentId = serverContent.id else { continue }
      let content = try getOrCreateContent(
        from: serverContent,
        contentId: contentId,
        contentByServerId: &contentByServerId
      )

      if !existingCollection.content.contains(where: { $0 === content }) {
        existingCollection.content.append(content)
      }
    }
  }

  /// Creates a new collection from server data
  private func createNewCollection(
    from serverCollection: ServerCollectionDTO,
    collectionByServerId: inout [Int: Collection],
    contentByServerId: inout [Int: Content],
    isoFormatter: ISO8601DateFormatter
  ) throws {
    let newCollection = Collection(
      serverId: serverCollection.id,
      name: serverCollection.name,
      content: [],
      thumbnail: serverCollection.thumbnail,
      createdAt: isoFormatter.date(from: serverCollection.created),
      updatedAt: isoFormatter.date(from: serverCollection.updated)
    )

    for serverContent in serverCollection.contents {
      guard let contentId = serverContent.id else { continue }
      let content = try getOrCreateContent(
        from: serverContent,
        contentId: contentId,
        contentByServerId: &contentByServerId
      )
      newCollection.content.append(content)
    }

    modelContext.insert(newCollection)
    collectionByServerId[serverCollection.id] = newCollection
  }

  /// Gets existing content or creates new content from server data
  private func getOrCreateContent(
    from serverContent: ServerContentDTO,
    contentId: Int,
    contentByServerId: inout [Int: Content]
  ) throws -> Content {
    if let existingContent = contentByServerId[contentId] {
      return existingContent
    }

    let newContent = Content(
      serverId: contentId,
      detail: serverContent.detail,
      title: serverContent.title,
      mediaUrl: serverContent.mediaUrl,
      url: serverContent.url,
      updatedAt: Date(),
      createdAt: Date()
    )

    modelContext.insert(newContent)
    contentByServerId[contentId] = newContent
    return newContent
  }

  /// Clears all collections from SwiftData
  public func clearAllCollections() async throws {
    let descriptor = FetchDescriptor<Collection>()
    let allCollections = try modelContext.fetch(descriptor)

    // Delete each collection (clear content relationships first)
    for collection in allCollections {
      collection.content = []
      modelContext.delete(collection)
    }

    try modelContext.save()
  }

  // MARK: - Data Management Operations

  /// Clears all user data from the database
  /// This includes Content, TrendingContent, RecentCollection, and Collection objects
  public func clearAllUserData() async throws {
    // Clear Content objects first (since collections may reference content)
    let contentDescriptor = FetchDescriptor<Content>()
    let content = try modelContext.fetch(contentDescriptor)

    for contentItem in content {
      modelContext.delete(contentItem)
    }

    // Clear TrendingContent
    let trendingDescriptor = FetchDescriptor<TrendingContent>()
    let trendingContent = try modelContext.fetch(trendingDescriptor)

    for trendingItem in trendingContent {
      modelContext.delete(trendingItem)
    }

    // Clear RecentCollection
    let recentDescriptor = FetchDescriptor<RecentCollection>()
    let recentCollections = try modelContext.fetch(recentDescriptor)

    for collection in recentCollections {
      modelContext.delete(collection)
    }

    // Clear Collection
    let collectionDescriptor = FetchDescriptor<Collection>()
    let collections = try modelContext.fetch(collectionDescriptor)

    for collection in collections {
      modelContext.delete(collection)
    }

    try modelContext.save()
    print(
      "✅ Successfully cleared data: \(content.count) Content, \(trendingContent.count) TrendingContent, \(recentCollections.count) RecentCollections, and \(collections.count) Collections"
    )
  }

  // Add other CRUD methods as needed, always using self.modelContext
}
